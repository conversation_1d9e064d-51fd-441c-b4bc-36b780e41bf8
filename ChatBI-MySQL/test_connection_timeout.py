#!/usr/bin/env python3
"""
测试数据库连接超时功能的脚本
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.db.connection import get_db_connection, CHATBI_POOL_SIZE, CHATBI_POOL_GET_TIMEOUT
from src.db.database_enum import Database
from src.utils.logger import logger

def test_connection_timeout():
    """测试连接池超时功能"""
    print(f"开始测试连接池超时功能...")
    print(f"连接池大小: {CHATBI_POOL_SIZE}")
    print(f"获取连接超时时间: {CHATBI_POOL_GET_TIMEOUT}秒")
    
    # 用于存储连接的列表
    connections = []
    
    def hold_connection(connection_id):
        """持有一个连接一段时间"""
        try:
            print(f"线程 {connection_id}: 尝试获取连接...")
            start_time = time.time()
            conn = get_db_connection(Database.CHATBI)
            elapsed = time.time() - start_time
            print(f"线程 {connection_id}: 成功获取连接，耗时 {elapsed:.2f}秒")
            
            # 持有连接一段时间
            time.sleep(10)
            
            conn.close()
            print(f"线程 {connection_id}: 连接已释放")
            return f"线程 {connection_id} 成功"
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"线程 {connection_id}: 获取连接失败，耗时 {elapsed:.2f}秒，错误: {e}")
            return f"线程 {connection_id} 失败: {e}"
    
    # 创建比连接池大小更多的线程来测试超时
    num_threads = CHATBI_POOL_SIZE + 3
    print(f"创建 {num_threads} 个线程来测试连接池...")
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有任务
        futures = [executor.submit(hold_connection, i) for i in range(num_threads)]
        
        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                result = future.result()
                print(f"任务完成: {result}")
            except Exception as e:
                print(f"任务异常: {e}")

def test_quick_connections():
    """测试快速获取和释放连接"""
    print(f"\n开始测试快速连接获取和释放...")
    
    for i in range(10):
        try:
            start_time = time.time()
            conn = get_db_connection(Database.CHATBI)
            elapsed = time.time() - start_time
            print(f"快速测试 {i+1}: 获取连接成功，耗时 {elapsed:.3f}秒")
            conn.close()
        except Exception as e:
            print(f"快速测试 {i+1}: 获取连接失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("数据库连接超时测试")
    print("=" * 60)
    
    # 首先测试快速连接
    test_quick_connections()
    
    # 然后测试超时功能
    test_connection_timeout()
    
    print("\n测试完成!")
